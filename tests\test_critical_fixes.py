#!/usr/bin/env python3
"""
Critical Trading System Error Fix Validation
Test the fixes for missing method and order amount calculation precision errors
"""

import sys
import os
import asyncio
import logging
from decimal import Decimal
import time

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CriticalFixValidator:
    """Validator for critical trading system fixes"""
    
    def __init__(self):
        self.test_results = {}
        
    async def run_validation_tests(self):
        """Run all critical fix validation tests"""
        logger.info("🔧 Starting Critical Fix Validation...")
        
        try:
            # Test 1: Missing Method Fix
            await self._test_missing_method_fix()
            
            # Test 2: Order Amount Calculation Fix
            await self._test_order_amount_calculation_fix()
            
            # Test 3: Balance Validation Enhancement
            await self._test_balance_validation_enhancement()
            
            # Test 4: Decimal Precision Handling
            await self._test_decimal_precision_handling()
            
            # Test 5: Integration Test
            await self._test_integration_with_fixes()
            
            # Generate validation report
            self._generate_validation_report()
            
            return self.test_results
            
        except Exception as e:
            logger.error(f"❌ Critical fix validation failed: {e}")
            return {'error': str(e)}
    
    async def _test_missing_method_fix(self):
        """Test that the missing get_comprehensive_status_report method is now available"""
        logger.info("🔍 Testing Missing Method Fix...")
        
        test_name = "missing_method_fix"
        self.test_results[test_name] = {'status': 'running', 'details': {}}
        
        try:
            # Import and initialize the MultiCurrencyTradingEngine
            from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
            
            # Create a minimal engine instance
            engine = MultiCurrencyTradingEngine({})
            
            # Test that the method exists
            if hasattr(engine, 'get_comprehensive_status_report'):
                self.test_results[test_name]['details']['method_exists'] = 'success'
                logger.info("✅ get_comprehensive_status_report method exists")
                
                # Test that the method is callable
                try:
                    # Mock exchange clients for testing
                    engine.exchange_clients = {}
                    engine.error_recovery = None
                    
                    status_report = await engine.get_comprehensive_status_report()
                    
                    if isinstance(status_report, dict) and 'timestamp' in status_report:
                        self.test_results[test_name]['details']['method_callable'] = 'success'
                        self.test_results[test_name]['details']['report_structure'] = list(status_report.keys())
                        logger.info("✅ get_comprehensive_status_report method is callable and returns proper structure")
                    else:
                        self.test_results[test_name]['details']['method_callable'] = 'failed'
                        logger.error("❌ get_comprehensive_status_report returns invalid structure")
                        
                except Exception as e:
                    self.test_results[test_name]['details']['method_callable'] = f'failed: {e}'
                    logger.error(f"❌ get_comprehensive_status_report method call failed: {e}")
            else:
                self.test_results[test_name]['details']['method_exists'] = 'failed'
                logger.error("❌ get_comprehensive_status_report method does not exist")
            
            self.test_results[test_name]['status'] = 'completed'
            
        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Missing method fix test failed: {e}")
    
    async def _test_order_amount_calculation_fix(self):
        """Test that order amount calculations are properly bounded and validated"""
        logger.info("💰 Testing Order Amount Calculation Fix...")
        
        test_name = "order_amount_calculation_fix"
        self.test_results[test_name] = {'status': 'running', 'details': {}}
        
        try:
            # Import the DynamicPortfolioRebalancer
            from src.trading.dynamic_portfolio_rebalancer import DynamicPortfolioRebalancer, RebalancingAction
            
            # Create test rebalancer
            rebalancer = DynamicPortfolioRebalancer({}, {
                'strategy': 'volatility_adjusted',
                'threshold': 0.05,
                'min_amount': 10.0,
                'max_single': 0.2,
                'max_allocation': 0.4,
                'min_allocation': 0.05
            })
            
            # Test 1: Large amount validation
            try:
                large_action = RebalancingAction(
                    currency_from='SOL',
                    currency_to='BTC',
                    amount=Decimal('8806.473807318056644472268700'),  # The problematic amount from logs
                    percentage=Decimal('0.1'),
                    reason='Test large amount',
                    priority=1,
                    expected_impact=Decimal('0.1'),
                    risk_adjustment=1.0
                )
                
                # Mock exchange client
                class MockClient:
                    def get_price(self, symbol):
                        return 0.002  # SOL/BTC price
                
                rebalancer.exchange_clients = {'test': MockClient()}
                
                # Test the calculation
                trade_amount = await rebalancer._calculate_trade_amount(
                    large_action, 'SOLBTC', 'sell', 'test'
                )
                
                # Should be capped at reasonable value
                if trade_amount <= Decimal('10'):  # Should be capped
                    self.test_results[test_name]['details']['large_amount_capping'] = 'success'
                    logger.info(f"✅ Large amount properly capped: {trade_amount}")
                else:
                    self.test_results[test_name]['details']['large_amount_capping'] = f'failed: {trade_amount}'
                    logger.error(f"❌ Large amount not properly capped: {trade_amount}")
                    
            except Exception as e:
                self.test_results[test_name]['details']['large_amount_capping'] = f'error: {e}'
                logger.error(f"❌ Large amount test error: {e}")
            
            # Test 2: Decimal precision validation
            try:
                precision_action = RebalancingAction(
                    currency_from='SOL',
                    currency_to='USDT',
                    amount=Decimal('50.123456789012345678901234567890'),  # Extreme precision
                    percentage=Decimal('0.1'),
                    reason='Test precision',
                    priority=1,
                    expected_impact=Decimal('0.1'),
                    risk_adjustment=1.0
                )
                
                trade_amount = await rebalancer._calculate_trade_amount(
                    precision_action, 'SOLUSDT', 'sell', 'test'
                )
                
                # Check if precision is properly handled (should be rounded to 8 decimal places)
                if trade_amount.as_tuple().exponent >= -8:
                    self.test_results[test_name]['details']['precision_handling'] = 'success'
                    logger.info(f"✅ Decimal precision properly handled: {trade_amount}")
                else:
                    self.test_results[test_name]['details']['precision_handling'] = f'failed: {trade_amount}'
                    logger.error(f"❌ Decimal precision not properly handled: {trade_amount}")
                    
            except Exception as e:
                self.test_results[test_name]['details']['precision_handling'] = f'error: {e}'
                logger.error(f"❌ Precision handling test error: {e}")
            
            self.test_results[test_name]['status'] = 'completed'
            
        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Order amount calculation fix test failed: {e}")
    
    async def _test_balance_validation_enhancement(self):
        """Test enhanced balance validation in MultiCurrencyTradingEngine"""
        logger.info("⚖️ Testing Balance Validation Enhancement...")
        
        test_name = "balance_validation_enhancement"
        self.test_results[test_name] = {'status': 'running', 'details': {}}
        
        try:
            from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine, TradingOpportunity, CurrencyPair
            
            engine = MultiCurrencyTradingEngine({})
            engine.exchange_clients = {}  # Mock empty clients
            
            # Test 1: Extreme amount validation
            try:
                extreme_opportunity = TradingOpportunity(
                    pair=CurrencyPair(
                        base_currency='SOL',
                        quote_currency='BTC',
                        symbol='SOLBTC',
                        exchange='test',
                        min_order_value=Decimal('0.001')
                    ),
                    side='sell',
                    amount=Decimal('8806.473807318056644472268700'),  # Extreme amount
                    price=Decimal('0.002'),
                    expected_profit=Decimal('0.01'),
                    confidence=0.8,
                    strategy='test',
                    risk_score=0.5,
                    execution_priority=1
                )
                
                validation_result = await engine._validate_balance_for_opportunity(extreme_opportunity)
                
                if not validation_result.get('valid', True):  # Should be invalid
                    self.test_results[test_name]['details']['extreme_amount_rejection'] = 'success'
                    logger.info(f"✅ Extreme amount properly rejected: {validation_result['reason']}")
                else:
                    self.test_results[test_name]['details']['extreme_amount_rejection'] = 'failed'
                    logger.error("❌ Extreme amount not properly rejected")
                    
            except Exception as e:
                self.test_results[test_name]['details']['extreme_amount_rejection'] = f'error: {e}'
                logger.error(f"❌ Extreme amount validation test error: {e}")
            
            # Test 2: Extreme precision validation
            try:
                precision_opportunity = TradingOpportunity(
                    pair=CurrencyPair(
                        base_currency='SOL',
                        quote_currency='USDT',
                        symbol='SOLUSDT',
                        exchange='test',
                        min_order_value=Decimal('5')
                    ),
                    side='sell',
                    amount=Decimal('10.123456789012345678901234567890'),  # Extreme precision
                    price=Decimal('95.0'),
                    expected_profit=Decimal('0.01'),
                    confidence=0.8,
                    strategy='test',
                    risk_score=0.5,
                    execution_priority=1
                )
                
                validation_result = await engine._validate_balance_for_opportunity(precision_opportunity)
                
                if not validation_result.get('valid', True):  # Should be invalid due to precision
                    self.test_results[test_name]['details']['precision_rejection'] = 'success'
                    logger.info(f"✅ Extreme precision properly rejected: {validation_result['reason']}")
                else:
                    self.test_results[test_name]['details']['precision_rejection'] = 'failed'
                    logger.error("❌ Extreme precision not properly rejected")
                    
            except Exception as e:
                self.test_results[test_name]['details']['precision_rejection'] = f'error: {e}'
                logger.error(f"❌ Precision validation test error: {e}")
            
            self.test_results[test_name]['status'] = 'completed'
            
        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Balance validation enhancement test failed: {e}")
    
    async def _test_decimal_precision_handling(self):
        """Test decimal precision handling across the system"""
        logger.info("🔢 Testing Decimal Precision Handling...")
        
        test_name = "decimal_precision_handling"
        self.test_results[test_name] = {'status': 'running', 'details': {}}
        
        try:
            # Test various decimal operations that could cause precision issues
            test_cases = [
                {
                    'name': 'large_division',
                    'operation': lambda: Decimal('8806.473807318056644472268700') / Decimal('0.002'),
                    'expected_bounded': True
                },
                {
                    'name': 'precision_multiplication',
                    'operation': lambda: Decimal('10.123456789012345678901234567890') * Decimal('95.0'),
                    'expected_bounded': True
                },
                {
                    'name': 'small_division',
                    'operation': lambda: Decimal('1.0') / Decimal('0.000001'),
                    'expected_bounded': True
                }
            ]
            
            for test_case in test_cases:
                try:
                    result = test_case['operation']()
                    
                    # Check if result has reasonable precision (8 decimal places or less)
                    if result.as_tuple().exponent >= -8:
                        self.test_results[test_name]['details'][test_case['name']] = 'success'
                        logger.info(f"✅ {test_case['name']}: {result} (precision OK)")
                    else:
                        # Apply quantize to fix precision
                        quantized_result = result.quantize(Decimal('0.00000001'))
                        self.test_results[test_name]['details'][test_case['name']] = f'fixed: {quantized_result}'
                        logger.info(f"🔧 {test_case['name']}: {result} -> {quantized_result} (precision fixed)")
                        
                except Exception as e:
                    self.test_results[test_name]['details'][test_case['name']] = f'error: {e}'
                    logger.error(f"❌ {test_case['name']} failed: {e}")
            
            self.test_results[test_name]['status'] = 'completed'
            
        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Decimal precision handling test failed: {e}")
    
    async def _test_integration_with_fixes(self):
        """Test integration of all fixes together"""
        logger.info("🔗 Testing Integration with All Fixes...")
        
        test_name = "integration_with_fixes"
        self.test_results[test_name] = {'status': 'running', 'details': {}}
        
        try:
            # Test that the system can handle the problematic scenario from the logs
            from src.trading.multi_currency_trading_engine import MultiCurrencyTradingEngine
            
            engine = MultiCurrencyTradingEngine({})
            engine.exchange_clients = {}
            engine.error_recovery = None
            
            # Test 1: Status report generation
            try:
                status_report = await engine.get_comprehensive_status_report()
                if 'system_health' in status_report:
                    self.test_results[test_name]['details']['status_report_integration'] = 'success'
                    logger.info("✅ Status report integration working")
                else:
                    self.test_results[test_name]['details']['status_report_integration'] = 'failed'
                    logger.error("❌ Status report integration failed")
            except Exception as e:
                self.test_results[test_name]['details']['status_report_integration'] = f'error: {e}'
                logger.error(f"❌ Status report integration error: {e}")
            
            # Test 2: Portfolio rebalancer with extreme values
            try:
                from src.trading.dynamic_portfolio_rebalancer import DynamicPortfolioRebalancer
                
                rebalancer = DynamicPortfolioRebalancer({}, {
                    'strategy': 'volatility_adjusted',
                    'threshold': 0.05,
                    'min_amount': 10.0,
                    'max_single': 0.2
                })
                
                # Mock current allocations with extreme values
                rebalancer.current_allocations = {
                    'SOL': {'value': 8806.473807318056644472268700, 'percentage': 0.9},
                    'USDT': {'value': 100.0, 'percentage': 0.1}
                }
                
                # This should not crash and should produce reasonable rebalancing actions
                rebalancing_actions = await rebalancer.identify_rebalancing_needs()
                
                # Check that actions have reasonable amounts
                reasonable_actions = all(
                    action.amount <= Decimal('100') 
                    for action in rebalancing_actions
                )
                
                if reasonable_actions:
                    self.test_results[test_name]['details']['rebalancer_integration'] = 'success'
                    logger.info(f"✅ Rebalancer integration working with {len(rebalancing_actions)} reasonable actions")
                else:
                    self.test_results[test_name]['details']['rebalancer_integration'] = 'failed'
                    logger.error("❌ Rebalancer still producing unreasonable amounts")
                    
            except Exception as e:
                self.test_results[test_name]['details']['rebalancer_integration'] = f'error: {e}'
                logger.error(f"❌ Rebalancer integration error: {e}")
            
            self.test_results[test_name]['status'] = 'completed'
            
        except Exception as e:
            self.test_results[test_name]['status'] = 'failed'
            self.test_results[test_name]['error'] = str(e)
            logger.error(f"❌ Integration test failed: {e}")
    
    def _generate_validation_report(self):
        """Generate validation report"""
        logger.info("📋 Generating Validation Report...")
        
        total_tests = len(self.test_results)
        passed_tests = len([t for t in self.test_results.values() if t.get('status') == 'completed'])
        failed_tests = len([t for t in self.test_results.values() if t.get('status') == 'failed'])
        
        logger.info(f"🔧 CRITICAL FIX VALIDATION SUMMARY:")
        logger.info(f"   Total Tests: {total_tests}")
        logger.info(f"   Passed: {passed_tests}")
        logger.info(f"   Failed: {failed_tests}")
        
        success_rate = passed_tests / total_tests if total_tests > 0 else 0
        logger.info(f"   Success Rate: {success_rate:.1%}")
        
        # Detailed results
        for test_name, result in self.test_results.items():
            status = result.get('status', 'unknown')
            if status == 'completed':
                logger.info(f"   ✅ {test_name}: PASSED")
            elif status == 'failed':
                logger.error(f"   ❌ {test_name}: FAILED - {result.get('error', 'Unknown error')}")
            else:
                logger.warning(f"   ⚠️ {test_name}: {status}")

async def main():
    """Run critical fix validation"""
    print("🔧 Critical Trading System Error Fix Validation")
    print("=" * 55)
    
    validator = CriticalFixValidator()
    results = await validator.run_validation_tests()
    
    if 'error' in results:
        print(f"❌ Validation failed: {results['error']}")
        return False
    
    # Check overall success
    passed_tests = len([t for t in results.values() if t.get('status') == 'completed'])
    total_tests = len(results)
    success_rate = passed_tests / total_tests if total_tests > 0 else 0
    
    print(f"\n🎯 FINAL RESULT: {passed_tests}/{total_tests} tests passed ({success_rate:.1%})")
    
    if success_rate >= 0.8:  # 80% success rate threshold
        print("✅ Critical Fix Validation: PASSED")
        return True
    else:
        print("❌ Critical Fix Validation: FAILED")
        return False

if __name__ == "__main__":
    asyncio.run(main())
